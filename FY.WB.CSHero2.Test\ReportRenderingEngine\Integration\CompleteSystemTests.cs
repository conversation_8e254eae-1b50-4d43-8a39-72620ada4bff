using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Xunit;
using FY.WB.CSHero2.Domain.Entities;
using FY.WB.CSHero2.Domain.Interfaces;
using FY.WB.CSHero2.Infrastructure.Persistence;
using FY.WB.CSHero2.ReportRenderingEngine.Application.Services;
using FY.WB.CSHero2.ReportRenderingEngine.Infrastructure.Services;
using FY.WB.CSHero2.ReportRenderingEngine.Domain.Models;

namespace FY.WB.CSHero2.Test.ReportRenderingEngine.Integration
{
    /// <summary>
    /// End-to-end integration tests for the complete Report Rendering Engine V2 system
    /// </summary>
    public class CompleteSystemTests : IDisposable
    {
        private readonly ServiceProvider _serviceProvider;
        private readonly ApplicationDbContext _context;
        private readonly ITemplateService _templateService;
        private readonly IReportService _reportService;
        private readonly IVersioningService _versioningService;
        private readonly IComponentGenerator _componentGenerator;
        private readonly IReportRenderer _reportRenderer;
        private readonly IExportService _exportService;
        private readonly EnhancedVersioningService _enhancedVersioningService;
        private readonly VersionComparisonService _versionComparisonService;

        public CompleteSystemTests()
        {
            var services = new ServiceCollection();
            ConfigureServices(services);
            _serviceProvider = services.BuildServiceProvider();

            _context = _serviceProvider.GetRequiredService<ApplicationDbContext>();
            _templateService = _serviceProvider.GetRequiredService<ITemplateService>();
            _reportService = _serviceProvider.GetRequiredService<IReportService>();
            _versioningService = _serviceProvider.GetRequiredService<IVersioningService>();
            _componentGenerator = _serviceProvider.GetRequiredService<IComponentGenerator>();
            _reportRenderer = _serviceProvider.GetRequiredService<IReportRenderer>();
            _exportService = _serviceProvider.GetRequiredService<IExportService>();
            _enhancedVersioningService = _serviceProvider.GetRequiredService<EnhancedVersioningService>();
            _versionComparisonService = _serviceProvider.GetRequiredService<VersionComparisonService>();

            SeedTestData();
        }

        [Fact]
        public async Task CompleteReportWorkflow_ShouldWorkEndToEnd()
        {
            // Step 1: Create a template
            var templateRequest = new CreateTemplateRequest
            {
                Name = "Integration Test Template",
                Description = "Template for end-to-end testing",
                DocumentContent = CreateTestDocumentContent()
            };

            var template = await _templateService.CreateTemplateAsync(templateRequest);
            Assert.NotNull(template);

            // Step 2: Create a report from template
            var reportRequest = new CreateReportRequest
            {
                Name = "Integration Test Report",
                Description = "Report for end-to-end testing",
                TemplateId = template.Id,
                ClientId = await CreateTestClientAsync()
            };

            var report = await _reportService.CreateReportAsync(reportRequest);
            Assert.NotNull(report);

            // Step 3: Generate components for the report
            var componentRequest = new ComponentRequest
            {
                ReportId = report.Id,
                Data = CreateTestReportData(),
                Options = new ComponentGenerationOptions
                {
                    Framework = "NextJS",
                    UseTypeScript = true,
                    StyleFramework = "TailwindCSS",
                    IncludeAccessibility = true
                }
            };

            var componentResult = await _componentGenerator.GenerateComponentAsync(componentRequest);
            Assert.NotNull(componentResult);
            Assert.True(componentResult.Success);
            Assert.NotEmpty(componentResult.Components);

            // Step 4: Create initial version
            var version1 = await _versioningService.CreateVersionAsync(
                report.Id, 
                componentResult, 
                "Initial version with generated components");
            Assert.NotNull(version1);
            Assert.Equal(1, version1.VersionNumber);

            // Step 5: Render the report
            var renderRequest = new RenderRequest
            {
                ReportId = report.Id,
                Data = CreateTestReportData()
            };

            var renderResult = await _reportRenderer.RenderReportAsync(renderRequest);
            Assert.NotNull(renderResult);
            Assert.True(renderResult.Success);
            Assert.NotEmpty(renderResult.RenderedHtml);

            // Step 6: Create enhanced version with metadata
            var enhancedMetadata = new VersionMetadata
            {
                Description = "Enhanced version with metadata",
                Tags = new List<string> { "integration-test", "v2" },
                Priority = "High",
                VersionType = "Major"
            };

            var version2 = await _enhancedVersioningService.CreateVersionWithMetadataAsync(
                report.Id, 
                componentResult, 
                enhancedMetadata);
            Assert.NotNull(version2);
            Assert.Equal(2, version2.VersionNumber);

            // Step 7: Compare versions
            var comparison = await _versionComparisonService.CompareVersionsDetailedAsync(
                report.Id, 1, 2);
            Assert.NotNull(comparison);
            Assert.Equal(report.Id, comparison.ReportId);

            // Step 8: Export to multiple formats
            var exportOptions = new ExportOptions
            {
                Quality = "High",
                IncludeImages = true,
                IncludeCoverPage = true
            };

            var pdfExport = await _exportService.ExportToPdfAsync(report.Id, exportOptions);
            Assert.NotNull(pdfExport);
            Assert.True(pdfExport.Length > 0);

            var htmlExport = await _exportService.ExportToHtmlAsync(report.Id, exportOptions);
            Assert.NotNull(htmlExport);
            Assert.Contains("<!DOCTYPE html>", htmlExport);

            // Step 9: Test rollback functionality
            var rollbackOptions = new RollbackOptions
            {
                CreateBackup = true,
                Reason = "Testing rollback in integration test"
            };

            var rollbackResult = await _versionComparisonService.PerformSafeRollbackAsync(
                report.Id, 1, rollbackOptions);
            Assert.NotNull(rollbackResult);
            Assert.True(rollbackResult.Success);

            // Step 10: Verify rollback worked
            var currentVersion = await _versioningService.GetCurrentVersionAsync(report.Id);
            Assert.Equal(1, currentVersion.VersionNumber);

            // Step 11: Get version history
            var history = await _enhancedVersioningService.GetEnhancedVersionHistoryAsync(report.Id);
            Assert.NotNull(history);
            Assert.True(history.Count >= 2); // Should have at least 2 versions plus backup
        }

        [Fact]
        public async Task ComponentGenerationAndVersioning_ShouldMaintainConsistency()
        {
            // Create test report
            var reportId = await CreateTestReportAsync();

            // Generate components multiple times with different options
            var frameworks = new[] { "NextJS", "React", "Vue" };
            var versions = new List<ReportVersion>();

            for (int i = 0; i < frameworks.Length; i++)
            {
                var componentRequest = new ComponentRequest
                {
                    ReportId = reportId,
                    Data = CreateTestReportData(),
                    Options = new ComponentGenerationOptions
                    {
                        Framework = frameworks[i],
                        UseTypeScript = true,
                        StyleFramework = "TailwindCSS"
                    }
                };

                var componentResult = await _componentGenerator.GenerateComponentAsync(componentRequest);
                Assert.True(componentResult.Success);

                var version = await _versioningService.CreateVersionAsync(
                    reportId, 
                    componentResult, 
                    $"Version with {frameworks[i]} components");
                
                versions.Add(version);
                Assert.Equal(i + 1, version.VersionNumber);
            }

            // Verify all versions exist and are properly sequenced
            var history = await _versioningService.GetVersionHistoryAsync(reportId);
            Assert.Equal(frameworks.Length, history.Count);
            Assert.True(history.Last().IsCurrent); // Latest should be current

            // Compare first and last versions
            var comparison = await _enhancedVersioningService.CompareVersionsAsync(
                reportId, 1, frameworks.Length);
            Assert.NotNull(comparison);
            Assert.True(comparison.Summary.TotalChanges > 0);
        }

        [Fact]
        public async Task ExportWithDifferentFormats_ShouldProduceValidOutputs()
        {
            // Create test report with content
            var reportId = await CreateTestReportWithContentAsync();

            var formats = await _exportService.GetSupportedFormatsAsync();
            Assert.NotEmpty(formats);

            foreach (var format in formats)
            {
                // Get capabilities for this format
                var capabilities = await _exportService.GetExportCapabilitiesAsync(format);
                Assert.NotNull(capabilities);
                Assert.Equal(format, capabilities.Format);

                // Generate preview
                var preview = await _exportService.PreviewExportAsync(reportId, format, new ExportOptions());
                Assert.NotNull(preview);
                Assert.True(preview.EstimatedFileSize > 0);

                // Perform actual export based on format
                if (format == "PDF")
                {
                    var pdfResult = await _exportService.ExportToPdfAsync(reportId, new ExportOptions());
                    Assert.NotNull(pdfResult);
                    Assert.True(pdfResult.Length > 0);
                }
                else if (format == "HTML")
                {
                    var htmlResult = await _exportService.ExportToHtmlAsync(reportId, new ExportOptions());
                    Assert.NotNull(htmlResult);
                    Assert.Contains("html", htmlResult.ToLower());
                }
            }
        }

        [Fact]
        public async Task BranchingAndMerging_ShouldWorkCorrectly()
        {
            // Create base report and version
            var reportId = await CreateTestReportWithContentAsync();

            // Create a branch
            var branchVersion = await _enhancedVersioningService.CreateBranchAsync(
                reportId, 1, "feature-branch", "Feature development branch");
            Assert.NotNull(branchVersion);
            Assert.Contains("feature-branch", branchVersion.Description);

            // Make changes in the branch (simulate by creating new version)
            var branchComponents = new ComponentResult
            {
                Success = true,
                Components = new List<SectionComponent>
                {
                    new SectionComponent
                    {
                        SectionId = "branch-feature",
                        SectionName = "Branch Feature",
                        ComponentCode = "const BranchFeature = () => <div>Branch feature content</div>;"
                    }
                }
            };

            var branchMetadata = new VersionMetadata
            {
                Description = "Branch feature implementation",
                BranchName = "feature-branch",
                IsBranch = true
            };

            var updatedBranch = await _enhancedVersioningService.CreateVersionWithMetadataAsync(
                reportId, branchComponents, branchMetadata);
            Assert.NotNull(updatedBranch);

            // Compare branch with main
            var comparison = await _versionComparisonService.CompareVersionsDetailedAsync(
                reportId, 1, updatedBranch.VersionNumber);
            Assert.NotNull(comparison);
            Assert.True(comparison.ImpactAnalysis.ComponentsAffected > 0);
        }

        [Fact]
        public async Task PerformanceWithLargeDataset_ShouldHandleEfficiently()
        {
            // Create report with large dataset
            var reportId = await CreateTestReportAsync();
            var largeDataset = CreateLargeTestDataset();

            var componentRequest = new ComponentRequest
            {
                ReportId = reportId,
                Data = largeDataset,
                Options = new ComponentGenerationOptions
                {
                    Framework = "NextJS",
                    UseTypeScript = true,
                    OptimizeForPerformance = true
                }
            };

            var startTime = DateTime.UtcNow;

            // Generate components
            var componentResult = await _componentGenerator.GenerateComponentAsync(componentRequest);
            Assert.True(componentResult.Success);

            // Create version
            var version = await _versioningService.CreateVersionAsync(
                reportId, componentResult, "Large dataset version");
            Assert.NotNull(version);

            // Export to PDF
            var exportOptions = new ExportOptions { Quality = "Medium" };
            var pdfResult = await _exportService.ExportToPdfAsync(reportId, exportOptions);
            Assert.NotNull(pdfResult);

            var endTime = DateTime.UtcNow;
            var totalTime = endTime - startTime;

            // Should complete within reasonable time (adjust threshold as needed)
            Assert.True(totalTime.TotalSeconds < 30, $"Operation took {totalTime.TotalSeconds} seconds");
        }

        #region Helper Methods

        private void ConfigureServices(IServiceCollection services)
        {
            // Add DbContext
            services.AddDbContext<ApplicationDbContext>(options =>
                options.UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString()));

            // Add logging
            services.AddLogging(builder => builder.AddConsole());

            // Add Report Rendering Engine services
            services.AddScoped<ITemplateService, TemplateServiceImpl>();
            services.AddScoped<IReportService, ReportServiceImpl>();
            services.AddScoped<IVersioningService, VersioningServiceImpl>();
            services.AddScoped<IComponentGenerator, ComponentGeneratorImpl>();
            services.AddScoped<IReportRenderer, ReportRendererV2>();
            services.AddScoped<IExportService, ExportServiceImpl>();
            services.AddScoped<EnhancedVersioningService>();
            services.AddScoped<VersionComparisonService>();

            // Add supporting services
            services.AddScoped<ComponentBuilder>();
            services.AddScoped<ComponentGenerationWorkflow>();
            services.AddScoped<EnhancedPromptBuilder>();

            // Add export providers
            services.AddScoped<IExportProvider, PdfExportProvider>();
            services.AddScoped<IExportProvider, HtmlExportProvider>();

            // Add infrastructure services
            services.AddMemoryCache();
            services.AddHttpClient();
        }

        private async Task<Guid> CreateTestClientAsync()
        {
            var client = new Client
            {
                Id = Guid.NewGuid(),
                Name = "Integration Test Client",
                Email = "<EMAIL>"
            };

            _context.Clients.Add(client);
            await _context.SaveChangesAsync();
            return client.Id;
        }

        private async Task<Guid> CreateTestReportAsync()
        {
            var clientId = await CreateTestClientAsync();
            
            var template = new Template
            {
                Id = Guid.NewGuid(),
                Name = "Test Template",
                Description = "Template for integration testing"
            };

            var report = new Report
            {
                Id = Guid.NewGuid(),
                Name = "Integration Test Report",
                Description = "Report for integration testing",
                ClientId = clientId,
                TemplateId = template.Id,
                Template = template
            };

            _context.Templates.Add(template);
            _context.Reports.Add(report);
            await _context.SaveChangesAsync();

            return report.Id;
        }

        private async Task<Guid> CreateTestReportWithContentAsync()
        {
            var reportId = await CreateTestReportAsync();

            var componentRequest = new ComponentRequest
            {
                ReportId = reportId,
                Data = CreateTestReportData(),
                Options = new ComponentGenerationOptions
                {
                    Framework = "NextJS",
                    UseTypeScript = true,
                    StyleFramework = "TailwindCSS"
                }
            };

            var componentResult = await _componentGenerator.GenerateComponentAsync(componentRequest);
            await _versioningService.CreateVersionAsync(reportId, componentResult, "Initial version");

            return reportId;
        }

        private string CreateTestDocumentContent()
        {
            return @"
                <sections>
                    <section id='introduction' name='Introduction'>
                        <field name='title' type='String' />
                        <field name='summary' type='String' />
                    </section>
                    <section id='data-analysis' name='Data Analysis'>
                        <field name='charts' type='Chart' />
                        <field name='metrics' type='Number' />
                    </section>
                </sections>";
        }

        private Dictionary<string, object> CreateTestReportData()
        {
            return new Dictionary<string, object>
            {
                { "title", "Integration Test Report" },
                { "summary", "This is a comprehensive integration test" },
                { "charts", new { type = "bar", data = new[] { 1, 2, 3, 4, 5 } } },
                { "metrics", 42 },
                { "date", DateTime.Now.ToString("yyyy-MM-dd") }
            };
        }

        private Dictionary<string, object> CreateLargeTestDataset()
        {
            var data = new Dictionary<string, object>();
            
            // Add many fields to simulate large dataset
            for (int i = 0; i < 100; i++)
            {
                data[$"field_{i}"] = $"value_{i}";
                data[$"number_{i}"] = i * 10;
                data[$"array_{i}"] = Enumerable.Range(0, 50).ToArray();
            }

            return data;
        }

        private void SeedTestData()
        {
            // Add any common test data here
        }

        #endregion

        public void Dispose()
        {
            _serviceProvider?.Dispose();
        }
    }
}
