using System;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using NUnit.Framework;

namespace FY.WB.CSHero2.Test
{
    public abstract class TestBase
    {
        protected readonly HttpClient _client;
        protected readonly string _baseUrl;
        protected readonly JsonSerializerOptions _jsonOptions;

        // Test tenant credentials - updated with actual seeded users
        protected readonly string _adminEmail = "<EMAIL>";
        protected readonly string _adminPassword = "AdminPass123!";
        
        // Using Jessica Lee (Health Plus) as tenant1 for tests
        protected readonly string _tenant1Email = "<EMAIL>"; 
        protected readonly string _tenant1Password = "Test123!";
        protected readonly Guid _tenant1Id = Guid.Parse("00000000-0000-0000-0003-000000000002"); // Health Plus tenant ID

        protected TestBase()
        {
            _client = new HttpClient();
            _baseUrl = "http://localhost:5056"; // Assuming the API is running on this port
            _client.BaseAddress = new Uri(_baseUrl);

            _jsonOptions = new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true,
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            };

            // Ensure the API is running
            EnsureApiIsRunning().Wait();
        }

        private async Task EnsureApiIsRunning()
        {
            try
            {
                var response = await _client.GetAsync("/api/health");
                if (!response.IsSuccessStatusCode)
                {
                    Assert.Fail("API is not running or health check failed. Please start the API before running tests.");
                }
            }
            catch (Exception ex)
            {
                Assert.Fail($"Failed to connect to API: {ex.Message}. Please ensure the API is running on {_baseUrl}");
            }
        }

        // New helper methods for getting tokens with known working credentials
        protected async Task<string> GetAdminToken()
        {
            return await GetAuthTokenForTenant(_adminEmail, _adminPassword);
        }

        protected async Task<string> GetTenant1Token()
        {
            return await GetAuthTokenForTenant(_tenant1Email, _tenant1Password);
        }

        protected async Task<string> GetAuthTokenForTenant(string email, string password)
        {
            var loginModel = new
            {
                Email = email,
                Password = password
            };

            var content = new StringContent(
                JsonSerializer.Serialize(loginModel, _jsonOptions),
                Encoding.UTF8,
                "application/json");

            var response = await _client.PostAsync("/api/auth/login", content);

            if (!response.IsSuccessStatusCode)
            {
                var errorContent = await response.Content.ReadAsStringAsync();
                Assert.Fail($"Failed to login with credentials {email}/{password}. Status: {response.StatusCode}, Error: {errorContent}");
            }

            var responseString = await response.Content.ReadAsStringAsync();
            var responseObject = JsonSerializer.Deserialize<LoginResponse>(responseString, _jsonOptions);

            Assert.IsNotNull(responseObject?.Token, "Authentication token is null");
            return responseObject.Token;
        }

        protected HttpClient CreateAuthenticatedClient(string token)
        {
            var client = new HttpClient();
            client.BaseAddress = new Uri(_baseUrl);
            client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);
            return client;
        }

        // Helper methods to create authenticated clients directly
        protected async Task<HttpClient> CreateAdminClient()
        {
            var token = await GetAdminToken();
            return CreateAuthenticatedClient(token);
        }

        protected async Task<HttpClient> CreateTenant1Client()
        {
            var token = await GetTenant1Token();
            return CreateAuthenticatedClient(token);
        }

        private class LoginResponse
        {
            public string Token { get; set; }
            public string? UserId { get; set; }
            public string? Email { get; set; }
            public string? TenantId { get; set; }
            public bool? IsAdmin { get; set; }
            public bool Success { get; set; }
        }
    }
}
