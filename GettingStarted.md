# Getting Started with the SaaS Template Project

This guide will help you get the project up and running and understand the current development context.

## 1. Starting the Development Environment

You'll need two terminals to run the backend API and the frontend application simultaneously.

### Backend (ASP.NET Core API)

1.  Open a terminal.
2.  Navigate to the backend project directory:
    ```bash
    cd FY.WB.CSHero2
    ```
3.  Run the API:
    ```bash
    dotnet run
    ```
    The API will typically start on `http://localhost:5056` (or a similar port, check the terminal output).

### Frontend (Next.js UI)

1.  Open a second terminal.
2.  Navigate to the frontend project directory:
    ```bash
    cd FY.WB.CSHero2.UI
    ```
3.  Install dependencies (if you haven't already):
    ```bash
    npm install
    ```
4.  Run the frontend development server:
    ```bash
    npm run dev
    ```
    The frontend application will typically start on `http://localhost:3000`.

## 2. Interacting with <PERSON><PERSON> (AI Assistant)

To provide <PERSON><PERSON> with the most relevant context for this project, it's recommended to include `@memory-bank` at the beginning of your prompts. This helps <PERSON><PERSON> access the project-specific knowledge base.

For example:
`@memory-bank Please help me implement the Report entity integration.`

## 3. Project Plan and Current Status

### Project Plan Location

The detailed project plan, outlining phases and tasks for integrating the frontend with the backend API, can be found in:
[`memory-bank/frontend_backend_integration_plan.md`](memory-bank/frontend_backend_integration_plan.md)

### Current Development Focus & Next Steps

Based on the [`memory-bank/activeContext.md`](memory-bank/activeContext.md) and [`memory-bank/progress.md`](memory-bank/progress.md) documents:

*   **Completed:**
    *   Full backend and frontend integration for the `Client` entity.
    *   Backend restructuring to Clean Architecture.
    *   Git repository setup.

*   **Immediate Next Steps:**
    1.  **End-to-End Testing for `Client` Integration:** Manually test the display of client data on the dashboard and any CRUD operations for clients.
    2.  **Proceed with `Report` Entity Integration:**
        *   Phase 0: Discovery & Analysis for `Report`.
        *   Phase 1: API Endpoint Design (CQRS) for `Report`.
        *   Phase 2: Backend Implementation for `Report`.
        *   Phase 3: Seed Data Creation for `Report`.
        *   Phase 4: Frontend Integration for `Report`.
        *   Phase 5: Verification & Documentation for `Report`.
    3.  **Address Known Issues:**
        *   Resolve the Next.js `/client/dashboard` 404 error.
        *   Revisit the Finbuckle CS0308 compilation error related to `MultiTenantIdentityDbContext`.

Refer to [`memory-bank/progress.md`](memory-bank/progress.md) for a detailed checklist of tasks for the `Report` entity integration and other ongoing items.

## 4. Important Patterns & Preferences

Remember to adhere to the established patterns and preferences:
-   Build and fix bugs systematically.
-   Follow Clean Architecture principles for backend development.
-   Use CQRS with MediatR for application logic.
-   Implement seed data for development and testing.
-   The deployment target is Azure Static Web Apps + AppService + Database.