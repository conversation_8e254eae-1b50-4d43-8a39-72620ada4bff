using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Xunit;
using FY.WB.CSHero2.Domain.Entities;
using FY.WB.CSHero2.Domain.Interfaces;
using FY.WB.CSHero2.Infrastructure.Persistence;
using FY.WB.CSHero2.ReportRenderingEngine.Application.Services;
using FY.WB.CSHero2.ReportRenderingEngine.Domain.Models;

namespace FY.WB.CSHero2.Test.ReportRenderingEngine.Integration
{
    /// <summary>
    /// Integration tests for Enhanced Versioning Service
    /// </summary>
    public class EnhancedVersioningServiceTests : IDisposable
    {
        private readonly ApplicationDbContext _context;
        private readonly EnhancedVersioningService _service;
        private readonly ILogger<EnhancedVersioningService> _logger;

        public EnhancedVersioningServiceTests()
        {
            var options = new DbContextOptionsBuilder<ApplicationDbContext>()
                .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
                .Options;

            _context = new ApplicationDbContext(options);
            _logger = new LoggerFactory().CreateLogger<EnhancedVersioningService>();
            _service = new EnhancedVersioningService(_context, _logger);

            // Seed test data
            SeedTestData();
        }

        [Fact]
        public async Task CreateVersionWithMetadataAsync_ShouldCreateVersionWithEnhancedMetadata()
        {
            // Arrange
            var reportId = Guid.NewGuid();
            await CreateTestReport(reportId);

            var components = new ComponentResult
            {
                Success = true,
                Components = new List<SectionComponent>
                {
                    new SectionComponent
                    {
                        SectionId = "section1",
                        SectionName = "Test Section",
                        ComponentCode = "const TestComponent = () => <div>Test</div>;"
                    }
                }
            };

            var metadata = new VersionMetadata
            {
                Description = "Test version with metadata",
                Tags = new List<string> { "test", "integration" },
                Priority = "High",
                VersionType = "Major",
                ApprovalStatus = "Approved"
            };

            // Act
            var result = await _service.CreateVersionWithMetadataAsync(reportId, components, metadata);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(1, result.VersionNumber);
            Assert.Equal("Test version with metadata", result.Description);
            Assert.True(result.IsCurrent);
            Assert.True(result.ComponentDataSize > 0);

            // Verify in database
            var savedVersion = await _context.ReportVersions
                .FirstOrDefaultAsync(rv => rv.Id == result.Id);
            Assert.NotNull(savedVersion);
            Assert.Equal(reportId, savedVersion.ReportId);
        }

        [Fact]
        public async Task CompareVersionsAsync_ShouldReturnDetailedComparison()
        {
            // Arrange
            var reportId = Guid.NewGuid();
            await CreateTestReport(reportId);

            // Create first version
            var components1 = new ComponentResult
            {
                Success = true,
                Components = new List<SectionComponent>
                {
                    new SectionComponent
                    {
                        SectionId = "section1",
                        SectionName = "Original Section",
                        ComponentCode = "const OriginalComponent = () => <div>Original</div>;"
                    }
                }
            };

            var metadata1 = new VersionMetadata { Description = "Version 1" };
            var version1 = await _service.CreateVersionWithMetadataAsync(reportId, components1, metadata1);

            // Create second version with changes
            var components2 = new ComponentResult
            {
                Success = true,
                Components = new List<SectionComponent>
                {
                    new SectionComponent
                    {
                        SectionId = "section1",
                        SectionName = "Updated Section",
                        ComponentCode = "const UpdatedComponent = () => <div>Updated</div>;"
                    },
                    new SectionComponent
                    {
                        SectionId = "section2",
                        SectionName = "New Section",
                        ComponentCode = "const NewComponent = () => <div>New</div>;"
                    }
                }
            };

            var metadata2 = new VersionMetadata { Description = "Version 2 with changes" };
            var version2 = await _service.CreateVersionWithMetadataAsync(reportId, components2, metadata2);

            // Act
            var comparison = await _service.CompareVersionsAsync(reportId, 1, 2);

            // Assert
            Assert.NotNull(comparison);
            Assert.Equal(reportId, comparison.ReportId);
            Assert.Equal(1, comparison.Version1);
            Assert.Equal(2, comparison.Version2);
            Assert.NotEmpty(comparison.MetadataChanges);
            Assert.NotEmpty(comparison.ComponentChanges);
            Assert.NotNull(comparison.Summary);
            Assert.True(comparison.Summary.TotalChanges > 0);
        }

        [Fact]
        public async Task GetEnhancedVersionHistoryAsync_ShouldReturnCompleteHistory()
        {
            // Arrange
            var reportId = Guid.NewGuid();
            await CreateTestReport(reportId);

            // Create multiple versions
            for (int i = 1; i <= 3; i++)
            {
                var components = new ComponentResult
                {
                    Success = true,
                    Components = new List<SectionComponent>
                    {
                        new SectionComponent
                        {
                            SectionId = $"section{i}",
                            SectionName = $"Section {i}",
                            ComponentCode = $"const Component{i} = () => <div>Version {i}</div>;"
                        }
                    }
                };

                var metadata = new VersionMetadata
                {
                    Description = $"Version {i}",
                    Tags = new List<string> { $"v{i}" }
                };

                await _service.CreateVersionWithMetadataAsync(reportId, components, metadata);
            }

            // Act
            var history = await _service.GetEnhancedVersionHistoryAsync(reportId);

            // Assert
            Assert.NotNull(history);
            Assert.Equal(3, history.Count);
            Assert.True(history.First().IsCurrent); // Latest version should be current
            Assert.False(history.Skip(1).First().IsCurrent); // Previous versions should not be current
            
            // Verify ordering (latest first)
            Assert.Equal(3, history.First().VersionNumber);
            Assert.Equal(2, history.Skip(1).First().VersionNumber);
            Assert.Equal(1, history.Skip(2).First().VersionNumber);
        }

        [Fact]
        public async Task CreateBranchAsync_ShouldCreateBranchFromSourceVersion()
        {
            // Arrange
            var reportId = Guid.NewGuid();
            await CreateTestReport(reportId);

            // Create source version
            var components = new ComponentResult
            {
                Success = true,
                Components = new List<SectionComponent>
                {
                    new SectionComponent
                    {
                        SectionId = "section1",
                        SectionName = "Main Section",
                        ComponentCode = "const MainComponent = () => <div>Main</div>;"
                    }
                }
            };

            var metadata = new VersionMetadata { Description = "Main version" };
            var sourceVersion = await _service.CreateVersionWithMetadataAsync(reportId, components, metadata);

            // Act
            var branchVersion = await _service.CreateBranchAsync(
                reportId, 
                sourceVersion.VersionNumber, 
                "feature-branch", 
                "Feature development branch");

            // Assert
            Assert.NotNull(branchVersion);
            Assert.Equal(2, branchVersion.VersionNumber); // Should be next version number
            Assert.Contains("feature-branch", branchVersion.Description);
            Assert.Equal(sourceVersion.ComponentDataJson, branchVersion.ComponentDataJson); // Should copy data
            Assert.False(branchVersion.IsCurrent); // Branch should not be current by default
        }

        [Fact]
        public async Task CreateVersionWithMetadataAsync_WithMultipleVersions_ShouldMaintainVersionSequence()
        {
            // Arrange
            var reportId = Guid.NewGuid();
            await CreateTestReport(reportId);

            var components = new ComponentResult
            {
                Success = true,
                Components = new List<SectionComponent>
                {
                    new SectionComponent
                    {
                        SectionId = "section1",
                        SectionName = "Test Section",
                        ComponentCode = "const TestComponent = () => <div>Test</div>;"
                    }
                }
            };

            // Act - Create multiple versions
            var version1 = await _service.CreateVersionWithMetadataAsync(
                reportId, components, new VersionMetadata { Description = "Version 1" });
            
            var version2 = await _service.CreateVersionWithMetadataAsync(
                reportId, components, new VersionMetadata { Description = "Version 2" });
            
            var version3 = await _service.CreateVersionWithMetadataAsync(
                reportId, components, new VersionMetadata { Description = "Version 3" });

            // Assert
            Assert.Equal(1, version1.VersionNumber);
            Assert.Equal(2, version2.VersionNumber);
            Assert.Equal(3, version3.VersionNumber);

            // Only latest should be current
            Assert.False(version1.IsCurrent);
            Assert.False(version2.IsCurrent);
            Assert.True(version3.IsCurrent);

            // Verify in database
            var allVersions = await _context.ReportVersions
                .Where(rv => rv.ReportId == reportId)
                .OrderBy(rv => rv.VersionNumber)
                .ToListAsync();

            Assert.Equal(3, allVersions.Count);
            Assert.Single(allVersions.Where(v => v.IsCurrent));
        }

        [Fact]
        public async Task CompareVersionsAsync_WithNonExistentVersions_ShouldThrowException()
        {
            // Arrange
            var reportId = Guid.NewGuid();
            await CreateTestReport(reportId);

            // Act & Assert
            await Assert.ThrowsAsync<InvalidOperationException>(
                () => _service.CompareVersionsAsync(reportId, 1, 2));
        }

        [Fact]
        public async Task CreateBranchAsync_WithNonExistentSourceVersion_ShouldThrowException()
        {
            // Arrange
            var reportId = Guid.NewGuid();
            await CreateTestReport(reportId);

            // Act & Assert
            await Assert.ThrowsAsync<InvalidOperationException>(
                () => _service.CreateBranchAsync(reportId, 999, "test-branch", "Test branch"));
        }

        private async Task CreateTestReport(Guid reportId)
        {
            var client = new Client
            {
                Id = Guid.NewGuid(),
                Name = "Test Client",
                Email = "<EMAIL>"
            };

            var template = new Template
            {
                Id = Guid.NewGuid(),
                Name = "Test Template",
                Description = "Test template for integration tests"
            };

            var report = new Report
            {
                Id = reportId,
                Name = "Test Report",
                Description = "Test report for integration tests",
                ClientId = client.Id,
                Client = client,
                TemplateId = template.Id,
                Template = template
            };

            _context.Clients.Add(client);
            _context.Templates.Add(template);
            _context.Reports.Add(report);
            await _context.SaveChangesAsync();
        }

        private void SeedTestData()
        {
            // Add any common test data here if needed
        }

        public void Dispose()
        {
            _context.Dispose();
        }
    }
}
