// Types for the enhanced report editor

// Report information
export interface ReportInfo {
  title: string;
  description: string;
  client?: {
    'client-id': string;
    'company-name': string;
  };
  startDate: string;
  endDate: string;
}

// Section type
export interface Section {
  id: string;
  title: string;
  description?: string;
  type: string;
  content: Record<string, any>;
}

// Section content types
export interface TitlePageContent {
  title: string;
  subtitle: string;
  footer: string;
}

export interface ContentSectionContent {
  content: string;
}

export interface DataSectionContent {
  heading?: string;
  subheading?: string;
  data?: Record<string, any>;
  fieldId?: string;
  fieldType?: string;
  fieldName?: string;
  fieldContent?: string;
  fieldConfig?: {
    rows?: number;
    columns?: number;
    timelineDate?: string;
    timelineTitle?: string;
  };
}

// Field type for section detail panel
export type FieldType = 'String' | 'Number' | 'Date' | 'TextArea' | 'Table' | 'Image';

// Field interface for section detail panel
export interface Field {
  id: string;
  name: string;
  description?: string;
  type: FieldType;
  content?: string;
  config?: {
    rows?: number;
    columns?: number;
    timelineDate?: string;
    timelineTitle?: string;
  };
}

// Template type
export interface Template {
  id: string;
  name: string;
  description: string;
  category: string;
  tags: string[];
  thumbnailUrl: string;
  pages?: any[];
  sections?: any[];
  fields?: any[];
}

// Style Template type
export interface StyleTemplate {
  id: string;
  name: string;
  thumbnailUrl: string;
  templateUrl: string;
  category?: string;
  description?: string;
  pages?: {
    coverPage: string;
    contentPage: string;
    chartsPage: string;
  };
}

// Style options
export interface StyleOptions {
  colorScheme?: 'professional' | 'creative' | 'minimal' | 'modern' | string;
  typography?: 'serif' | 'sans' | 'mono' | string;
  spacing?: 'compact' | 'balanced' | 'airy' | string;
  styleTemplateId?: string;
  layout?: DocumentLayoutOptions;
  typographyOptions?: TypographyOptions;
  structure?: StructureOptions;
  content?: ContentFormattingOptions;
  visual?: VisualElementOptions;
}

// Document Layout Options
export interface DocumentLayoutOptions {
  pageSize?: 'letter' | 'a4' | 'legal' | 'tabloid' | 'custom';
  pageSizeDimensions?: {
    width: string;
    height: string;
  };
  orientation?: 'portrait' | 'landscape';
  margins?: {
    preset?: 'standard' | 'narrow' | 'moderate' | 'wide' | 'custom';
    top?: string;
    bottom?: string;
    left?: string;
    right?: string;
  };
  bleed?: 'none' | '0.125' | '0.25';
  columns?: {
    count?: 1 | 2 | 3 | 4 | 'custom';
    widths?: 'equal' | 'custom';
    gutterWidth?: '0.25' | '0.5' | '0.75' | 'custom';
    customGutterWidth?: string;
    separator?: 'none' | 'line' | 'custom';
  };
  coverDesign?: {
    layoutStyle?: 'minimal' | 'full-bleed' | 'split' | 'typography' | 'custom';
    customLayoutStyle?: string;
    background?: 'solid' | 'gradient' | 'image' | 'pattern' | 'none';
    titlePosition?: 'centered' | 'left' | 'right' | 'top' | 'bottom' | 'custom';
    customTitlePosition?: string;
    visualElements?: 'logo-only' | 'logo-with-graphics' | 'image' | 'abstract';
  };
  titlePage?: {
    title?: {
      font?: string;
      size?: string;
      color?: string;
      alignment?: 'left' | 'center' | 'right';
    };
    subtitle?: {
      show?: boolean;
      font?: string;
      size?: string;
      color?: string;
    };
    author?: {
      show?: boolean;
      format?: 'list' | 'grid';
      font?: string;
      size?: string;
    };
    date?: {
      show?: boolean;
      format?: 'month-year' | 'mm-dd-yyyy' | 'custom';
      customFormat?: string;
    };
    orgInfo?: {
      logo?: boolean;
      department?: boolean;
      contactInfo?: boolean;
    };
    disclaimers?: {
      show?: boolean;
      font?: string;
      size?: string;
    };
  };
  headerFooter?: {
    headerContent?: 'none' | 'title' | 'section' | 'custom';
    customHeaderContent?: string;
    footerContent?: 'none' | 'page-number' | 'date' | 'author' | 'custom';
    customFooterContent?: string;
    pageNumbering?: {
      style?: '1' | 'i' | 'I' | 'a' | 'A' | 'none';
      position?: 'bottom-center' | 'bottom-right' | 'bottom-left' | 'top-right' | 'custom';
      format?: 'page-x' | 'x-of-y' | 'x' | 'custom';
    };
    firstPage?: 'same' | 'different' | 'none';
    sectionBreaks?: 'continue' | 'restart';
  };
}

// Typography Options
export interface TypographyOptions {
  fontFamilies?: {
    primary?: string;
    secondary?: string;
    monospace?: string;
    pairingPreset?: 'classic' | 'modern' | 'technical' | 'creative' | 'custom';
  };
  fontSizes?: {
    body?: '10pt' | '11pt' | '12pt' | 'custom';
    headings?: {
      h1?: string;
      h2?: string;
      h3?: string;
      h4?: string;
      h5?: string;
    };
    footnotes?: '8pt' | '9pt' | '10pt' | 'custom';
    captions?: '8pt' | '9pt' | '10pt' | 'custom';
    scaleType?: 'fixed' | 'relative' | 'modular';
  };
  lineSpacing?: {
    body?: '1.0' | '1.15' | '2.0' | 'custom';
    headings?: '1.0' | '0.9' | '1.2' | 'custom';
    lists?: 'same-as-body' | 'custom';
    blockQuotes?: 'same-as-body' | 'custom';
    footnotes?: 'single' | 'custom';
  };
  alignment?: {
    body?: 'left' | 'justified' | 'center' | 'right';
    headings?: 'left' | 'center' | 'right';
    lists?: 'left' | 'custom';
    captions?: 'left' | 'center' | 'right';
    tables?: 'left' | 'center' | 'custom';
  };
  specialTextStyles?: {
    boldUsage?: 'headings-only' | 'key-terms' | 'custom';
    italicsUsage?: 'emphasis' | 'terms' | 'titles' | 'custom';
    underlineUsage?: 'none' | 'hyperlinks-only' | 'custom';
    highlighting?: 'none' | 'background' | 'text-color' | 'custom';
    textCase?: 'sentence' | 'title' | 'uppercase' | 'lowercase';
    smallCaps?: 'none' | 'headings-only' | 'custom';
  };
}

// Structure Options
export interface StructureOptions {
  headingLevels?: {
    count?: 1 | 2 | 3 | 4 | 5 | 6;
    distinction?: 'size' | 'weight' | 'color' | 'combined' | 'custom';
  };
  numberingScheme?: {
    style?: 'none' | 'decimal' | 'legal' | 'bullet' | 'custom';
    depth?: 'all' | number | 'custom';
    format?: 'number-only' | 'number-with-text' | 'custom';
    delimiter?: 'period' | 'hyphen' | 'space' | 'custom';
  };
  headingStyles?: {
    h1?: {
      font?: 'primary' | 'secondary' | 'custom';
      size?: string;
      weight?: 'regular' | 'bold' | 'custom';
      color?: 'primary' | 'secondary' | 'black' | 'custom';
      spacingBefore?: string;
      spacingAfter?: string;
      border?: 'none' | 'below' | 'custom';
    };
    h2?: HeadingStyle;
    h3?: HeadingStyle;
    h4?: HeadingStyle;
    h5?: HeadingStyle;
    h6?: HeadingStyle;
  };
  tableOfContents?: {
    depth?: 'all' | number;
    style?: 'simple' | 'hierarchical' | 'boxed' | 'custom';
    dotLeaders?: 'none' | 'dots' | 'line' | 'custom';
    pageNumbers?: boolean;
    links?: 'none' | 'hyperlinked';
  };
  sectionCoverPages?: {
    usage?: 'none' | 'major' | 'all' | 'custom';
    style?: 'minimal' | 'full-page' | 'image' | 'typography' | 'custom';
    elements?: string[];
    numbering?: 'continue' | 'restart' | 'none';
  };
  documentNavigation?: {
    index?: {
      style?: 'basic' | 'two-column' | 'custom';
      entryFormat?: 'bold' | 'italic' | 'plain' | 'custom';
      pageReference?: 'numbers' | 'ranges' | 'custom';
    };
    glossary?: {
      layout?: 'term-definition' | 'tabular' | 'custom';
      termStyle?: 'bold' | 'italic' | 'colored' | 'custom';
      definitionStyle?: 'plain' | 'indented' | 'custom';
    };
    crossReferences?: 'none' | 'page-numbers' | 'hyperlinks' | 'custom';
  };
}

// Content Formatting Options
export interface ContentFormattingOptions {
  bodyTextStyle?: {
    paragraphIndentation?: 'none' | 'first-line' | 'hanging' | 'custom';
    spacingBetweenParagraphs?: 'none' | string | 'full-line' | 'custom';
    textJustification?: 'left' | 'justified' | 'custom';
    hyphenation?: 'none' | 'automatic' | 'custom';
    dropCaps?: 'none' | 'first-paragraph' | 'custom';
  };
  lists?: {
    bulletStyle?: {
      level1?: 'filled-circle' | 'dash' | 'square' | 'custom';
      level2?: 'open-circle' | 'dash' | 'square' | 'custom';
      level3?: string;
    };
    numberedListStyle?: {
      level1?: '1-2-3' | 'a-b-c' | 'i-ii-iii' | 'custom';
      level2?: string;
      level3?: string;
    };
    indentation?: 'bullet-aligned' | 'hanging' | 'custom';
    spacing?: {
      betweenItems?: string;
      beforeAfterLists?: string;
    };
  };
  blockQuotes?: {
    indentation?: 'left' | 'right' | 'both' | 'custom';
    fontStyle?: 'same-as-body' | 'italicized' | 'smaller' | 'custom';
    bordersBackground?: 'none' | 'left-border' | 'background' | 'custom';
    attributionStyle?: 'none' | 'right-aligned' | 'below-with-dash' | 'custom';
  };
  tables?: {
    borderStyle?: 'none' | 'all' | 'outside' | 'custom';
    headerRowStyle?: 'bold' | 'background' | 'both' | 'custom';
    rowStriping?: 'none' | 'alternating' | 'custom';
    cellPadding?: 'minimal' | 'moderate' | 'spacious' | 'custom';
    textAlignment?: 'left' | 'centered' | 'context-dependent' | 'custom';
    captionPosition?: 'above' | 'below' | 'custom';
  };
  references?: {
    styleGuide?: 'apa' | 'mla' | 'chicago' | 'ieee' | 'harvard' | 'vancouver' | 'custom';
    bibliographyLayout?: 'hanging-indent' | 'block' | 'custom';
    citationFormat?: 'in-text' | 'footnotes' | 'endnotes' | 'custom';
    footnotePosition?: 'bottom-of-page' | 'end-of-section' | 'end-of-document';
    numberingStyle?: 'continuous' | 'restart' | 'custom';
  };
}

// Visual Element Options
export interface VisualElementOptions {
  colorPalette?: {
    primary?: string;
    secondary?: string;
    neutralBackground?: 'white' | 'off-white' | 'light-gray' | 'custom';
    neutralText?: 'black' | 'dark-gray' | 'dark-blue' | 'custom';
    accentColors?: string[];
    scheme?: 'monochromatic' | 'complementary' | 'analogous' | 'triadic' | 'corporate' | 'custom';
  };
  chartStyle?: {
    lineThickness?: 'thin' | 'medium' | 'thick' | 'custom';
    colors?: 'match-document' | 'specific' | 'custom';
    fonts?: 'match-document' | 'specific' | 'custom';
    gridLines?: 'none' | 'light' | 'dark' | 'custom';
    dataLabels?: 'none' | 'endpoints' | 'all' | 'custom';
    legend?: 'none' | 'right' | 'bottom' | 'custom';
    chartTypes?: string[];
  };
  imagePlacement?: {
    defaultPosition?: 'centered' | 'inline' | 'text-wrapped' | 'custom';
    textWrapping?: 'none' | 'square' | 'tight' | 'through' | 'custom';
    sizePresets?: 'small' | 'medium' | 'large' | 'full-width' | 'custom';
    border?: 'none' | 'thin-line' | 'frame' | 'custom';
    spacing?: 'tight' | 'standard' | 'generous' | 'custom';
  };
  captionStyle?: {
    position?: 'below' | 'above' | 'side' | 'custom';
    font?: 'same-as-body' | 'smaller' | 'italicized' | 'custom';
    numbering?: 'none' | 'figure-x' | 'custom';
    alignment?: 'left' | 'centered' | 'custom';
  };
  brandingElements?: {
    logo?: {
      placement?: 'cover-only' | 'every-page' | 'custom';
      size?: 'small' | 'medium' | 'large' | 'custom';
      colorVersion?: 'full-color' | 'monochrome' | 'custom';
    };
    watermarks?: {
      type?: 'none' | 'text' | 'logo' | 'pattern' | 'custom';
      opacity?: 'light' | 'medium' | 'dark' | 'custom';
    };
    brandTypography?: 'follow-guidelines' | 'document-overrides' | 'custom';
    brandColors?: 'strict' | 'modified' | 'custom';
  };
}

// Helper type for heading styles
export interface HeadingStyle {
  font?: 'primary' | 'secondary' | 'custom';
  size?: string;
  weight?: 'regular' | 'bold' | 'custom';
  color?: 'primary' | 'secondary' | 'black' | 'custom';
  spacingBefore?: string;
  spacingAfter?: string;
  border?: 'none' | 'below' | 'custom';
}

// Content Block types
export type BlockType =
  | 'title'
  | 'heading1'
  | 'heading2'
  | 'heading3'
  | 'heading4'
  | 'blockquote'
  | 'table2x2'
  | 'table3x3'
  | 'table4x4'
  | 'bulletedList'
  | 'numberedList'
  | 'todoList'
  | 'noteBox'
  | 'infoBox'
  | 'warningBox'
  | 'cautionBox'
  | 'successBox'
  | 'questionBox';

// Content Block category
export type BlockCategory =
  | 'text'
  | 'tables'
  | 'lists'
  | 'calloutBoxes'
  | 'interactive';

// Content Block interface
export interface ContentBlock {
  id: string;
  type: BlockType;
  category: BlockCategory;
  title: string;
  preview: string;
  icon: string;
  content: string;
}

// Column Content interface
export interface ColumnContent {
  blocks: ContentBlock[];
}

// Timeline Item interface
export interface TimelineItem {
  id?: string;
  date: string;
  title: string;
  content: string;
  image?: string;
}

// Component Props

// Main Editor Props
export interface ReportEditorProps {
  reportId?: string;
  templateId?: string;
  styleId?: string;
  onSave?: (report: any) => void;
  onCancel?: () => void;
}

// Sidebar Props
export interface SidebarProps {
  activeItem: string | null;
  onMenuClick: (item: string) => void;
  onSectionClick: (id: string, title: string) => void;
  onReturnToReports: () => void;
  onSaveReport: () => void;
  reportTitle: string;
}

// Panel Content Props
export interface PanelContentProps {
  title: string;
  onClose: () => void;
  onSectionClick?: (id: string, title: string) => void;
  // Report Info props
  reportInfo?: ReportInfo;
  clients?: any[];
  onUpdateReportInfo?: (field: string, value: any) => void;
  templateName?: string;
  // Sections props
  sections?: Section[];
  onAddSection?: () => void;
  onUpdateSection?: (section: Section) => void;
  // Templates props
  templates?: Template[];
  selectedTemplate?: string;
  onTemplateSelect?: (templateId: string) => void;
  // Style props
  styles?: StyleOptions;
  onUpdateStyle?: (field: string, value: any) => void;
}

// Section Detail Panel Props

export interface SectionDetailPanelProps {
  sectionId: string;
  title: string;
  onBack: () => void;
  section: Section | null;
  onUpdateSection?: (section: Section) => void;
}

// Chat Message type
export interface ChatMessage {
  id: string;
  content: string;
  sender: 'user' | 'system';
  timestamp: string;
  sectionId?: string;
}

// Preview Area Props
export interface PreviewAreaProps {
  section: Section | null;
  onUpdateSection?: (updatedSection: Section) => void;
  allSections?: Section[];
  styleTemplateId?: string;
  pulsingSectionId?: string | null;
}

// Chat Interface Props
export interface ChatInterfaceProps {
  messages: ChatMessage[];
  onSendMessage: (message: string, sectionId?: string) => void;
  activeSection: string | null;
  sections: { id: string; title: string }[];
  onSectionChange: (sectionId: string | null) => void;
}

// No preview component props needed

// Field Renderer Props
export interface FieldRendererProps {
  field: Field;
  onFieldUpdate?: (updatedField: Field) => void;
}

// Rich Text Editor Props
export interface RichTextEditorProps {
  content?: string;
  onChange?: (content: string) => void;
  placeholder?: string;
}
