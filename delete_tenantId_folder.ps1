# Delete the route.ts file
Remove-Item -Path "FY.WB.CSHero2.UI\src\app\api\v1\tenants\[tenantId]\invoices\route.ts" -Force -ErrorAction SilentlyContinue

# Delete the invoices directory
Remove-Item -Path "FY.WB.CSHero2.UI\src\app\api\v1\tenants\[tenantId]\invoices" -Force -Recurse -ErrorAction SilentlyContinue

# Delete the [tenantId] directory
Remove-Item -Path "FY.WB.CSHero2.UI\src\app\api\v1\tenants\[tenantId]" -Force -Recurse -ErrorAction SilentlyContinue

Write-Host "Deletion completed."
