{"Jwt": {"Key": "ThisIsAStrongAndLongEnoughSecretKeyForTestingPurposes!ChangeInProduction!", "Issuer": "http://localhost:5056", "Audience": "http://localhost:3000"}, "CompanyProfile": {"Name": "Children's Village Inc.", "AddressLine1": "123 Main Street", "AddressLine2": "Suite 100", "City": "Anytown", "State": "ST", "ZipCode": "12345", "Country": "USA", "Phone": "******-123-4567", "Email": "<EMAIL>", "Website": "https://www.childrensvillage.org", "LogoUrl": "/assets/images/logo.png"}, "AzureAd": {"Instance": "https://login.microsoftonline.com/", "Domain": "qualified.domain.name", "TenantId": "22222222-2222-2222-2222-222222222222", "ClientId": "11111111-1111-1111-11111111111111111", "Scopes": "access_as_user", "CallbackPath": "/signin-oidc"}, "ReportRenderingEngine": {"LLM": {"Provider": "OpenAI", "Model": "gpt-4", "ApiKey": "your-api-key-here", "MaxTokens": 4000, "TimeoutSeconds": 30}}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*"}