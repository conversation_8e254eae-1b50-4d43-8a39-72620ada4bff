# Delete frontend data modules
Write-Host "Deleting frontend data modules..."
$dataDir = "FY.WB.CSHero2.UI\src\lib\data"
$files = Get-ChildItem -Path $dataDir -File

foreach ($file in $files) {
    Write-Host "Deleting $($file.FullName)"
    Remove-Item -Path $file.FullName -Force
}

# Delete db.json
Write-Host "Deleting db.json..."
$dbJsonPath = "FY.WB.CSHero2.UI\src\data\db\db.json"
if (Test-Path $dbJsonPath) {
    Remove-Item -Path $dbJsonPath -Force
    Write-Host "Deleted $dbJsonPath"
} else {
    Write-Host "$dbJsonPath not found"
}

Write-Host "Frontend test data removal completed."
