using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Xunit;
using FY.WB.CSHero2.Domain.Entities;
using FY.WB.CSHero2.Domain.Interfaces;
using FY.WB.CSHero2.Infrastructure.Persistence;
using FY.WB.CSHero2.ReportRenderingEngine.Application.Services;
using FY.WB.CSHero2.ReportRenderingEngine.Domain.Models;

namespace FY.WB.CSHero2.Test.ReportRenderingEngine.Integration
{
    /// <summary>
    /// Integration tests for Version Comparison Service
    /// </summary>
    public class VersionComparisonServiceTests : IDisposable
    {
        private readonly ApplicationDbContext _context;
        private readonly VersionComparisonService _service;
        private readonly ILogger<VersionComparisonService> _logger;

        public VersionComparisonServiceTests()
        {
            var options = new DbContextOptionsBuilder<ApplicationDbContext>()
                .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
                .Options;

            _context = new ApplicationDbContext(options);
            _logger = new LoggerFactory().CreateLogger<VersionComparisonService>();
            _service = new VersionComparisonService(_context, _logger);

            // Seed test data
            SeedTestData();
        }

        [Fact]
        public async Task CompareVersionsDetailedAsync_ShouldReturnDetailedComparison()
        {
            // Arrange
            var reportId = Guid.NewGuid();
            await CreateTestReportWithVersions(reportId);

            // Act
            var comparison = await _service.CompareVersionsDetailedAsync(reportId, 1, 2);

            // Assert
            Assert.NotNull(comparison);
            Assert.Equal(reportId, comparison.ReportId);
            Assert.Equal(1, comparison.Version1);
            Assert.Equal(2, comparison.Version2);
            Assert.NotNull(comparison.Version1Info);
            Assert.NotNull(comparison.Version2Info);
            Assert.NotNull(comparison.ComponentComparisons);
            Assert.NotNull(comparison.DataComparisons);
            Assert.NotNull(comparison.ImpactAnalysis);
            Assert.NotNull(comparison.MigrationRecommendations);
            Assert.True(comparison.CompatibilityScore >= 0 && comparison.CompatibilityScore <= 100);
        }

        [Fact]
        public async Task AnalyzeRollbackImpactAsync_ShouldReturnImpactAnalysis()
        {
            // Arrange
            var reportId = Guid.NewGuid();
            await CreateTestReportWithVersions(reportId);

            // Act
            var analysis = await _service.AnalyzeRollbackImpactAsync(reportId, 1);

            // Assert
            Assert.NotNull(analysis);
            Assert.Equal(reportId, analysis.ReportId);
            Assert.Equal(2, analysis.CurrentVersion); // Should be current version
            Assert.Equal(1, analysis.TargetVersion);
            Assert.NotNull(analysis.DataLoss);
            Assert.NotNull(analysis.ComponentChanges);
            Assert.NotNull(analysis.FeatureImpact);
            Assert.NotNull(analysis.RiskAssessment);
            Assert.NotNull(analysis.Recommendations);
        }

        [Fact]
        public async Task CreateRollbackBackupAsync_ShouldCreateBackupVersion()
        {
            // Arrange
            var reportId = Guid.NewGuid();
            await CreateTestReportWithVersions(reportId);

            // Act
            var backupId = await _service.CreateRollbackBackupAsync(reportId, 1, "Testing rollback backup");

            // Assert
            Assert.NotEqual(Guid.Empty, backupId);

            // Verify backup was created
            var backup = await _context.ReportVersions.FindAsync(backupId);
            Assert.NotNull(backup);
            Assert.Equal(3, backup.VersionNumber); // Should be next version number
            Assert.Contains("Backup before rollback", backup.Description);
            Assert.False(backup.IsCurrent); // Backup should not be current
        }

        [Fact]
        public async Task PerformSafeRollbackAsync_ShouldRollbackSuccessfully()
        {
            // Arrange
            var reportId = Guid.NewGuid();
            await CreateTestReportWithVersions(reportId);

            var options = new RollbackOptions
            {
                CreateBackup = true,
                ForceRollback = false,
                Reason = "Testing safe rollback",
                ValidateBeforeRollback = true
            };

            // Act
            var result = await _service.PerformSafeRollbackAsync(reportId, 1, options);

            // Assert
            Assert.NotNull(result);
            Assert.True(result.Success);
            Assert.Equal(reportId, result.ReportId);
            Assert.Equal(1, result.TargetVersion);
            Assert.NotNull(result.BackupVersionId);
            Assert.NotNull(result.ValidationResult);
            Assert.True(result.ValidationResult.IsValid);

            // Verify rollback was performed
            var targetVersion = await _context.ReportVersions
                .FirstOrDefaultAsync(rv => rv.ReportId == reportId && rv.VersionNumber == 1);
            Assert.NotNull(targetVersion);
            Assert.True(targetVersion.IsCurrent);

            // Verify other versions are not current
            var otherVersions = await _context.ReportVersions
                .Where(rv => rv.ReportId == reportId && rv.VersionNumber != 1)
                .ToListAsync();
            Assert.All(otherVersions, v => Assert.False(v.IsCurrent));
        }

        [Fact]
        public async Task PerformSafeRollbackAsync_WithValidationFailure_ShouldFailWithoutForce()
        {
            // Arrange
            var reportId = Guid.NewGuid();
            await CreateTestReportWithVersions(reportId);

            var options = new RollbackOptions
            {
                CreateBackup = true,
                ForceRollback = false, // Don't force
                Reason = "Testing validation failure",
                ValidateBeforeRollback = true
            };

            // Act - Try to rollback to current version (should fail validation)
            var result = await _service.PerformSafeRollbackAsync(reportId, 2, options);

            // Assert
            Assert.NotNull(result);
            Assert.False(result.Success);
            Assert.Contains("validation failed", result.ErrorMessage);
        }

        [Fact]
        public async Task CompareVersionsDetailedAsync_WithComponentChanges_ShouldDetectChanges()
        {
            // Arrange
            var reportId = Guid.NewGuid();
            await CreateTestReportWithDifferentComponents(reportId);

            // Act
            var comparison = await _service.CompareVersionsDetailedAsync(reportId, 1, 2);

            // Assert
            Assert.NotNull(comparison);
            Assert.NotEmpty(comparison.ComponentComparisons);
            
            // Should detect component changes
            var componentChanges = comparison.ComponentComparisons;
            Assert.Contains(componentChanges, c => c.ChangeType == "Modified" || c.ChangeType == "Added" || c.ChangeType == "Removed");
            
            // Impact analysis should reflect changes
            Assert.True(comparison.ImpactAnalysis.ComponentsAffected > 0);
        }

        [Fact]
        public async Task AnalyzeRollbackImpactAsync_WithDataLoss_ShouldIdentifyLoss()
        {
            // Arrange
            var reportId = Guid.NewGuid();
            await CreateTestReportWithDataChanges(reportId);

            // Act
            var analysis = await _service.AnalyzeRollbackImpactAsync(reportId, 1);

            // Assert
            Assert.NotNull(analysis);
            Assert.NotNull(analysis.DataLoss);
            
            // Should identify data that would be lost
            if (analysis.DataLoss.LostDataFields.Any() || analysis.DataLoss.LostComponents.Any())
            {
                Assert.NotEqual("None", analysis.DataLoss.EstimatedDataLoss);
                Assert.NotEmpty(analysis.DataLoss.RecommendedActions);
            }
        }

        [Fact]
        public async Task CompareVersionsDetailedAsync_WithNonExistentVersions_ShouldThrowException()
        {
            // Arrange
            var reportId = Guid.NewGuid();
            await CreateTestReport(reportId);

            // Act & Assert
            await Assert.ThrowsAsync<InvalidOperationException>(
                () => _service.CompareVersionsDetailedAsync(reportId, 1, 999));
        }

        private async Task CreateTestReportWithVersions(Guid reportId)
        {
            await CreateTestReport(reportId);

            // Create version 1
            var version1 = new ReportVersion
            {
                Id = Guid.NewGuid(),
                ReportId = reportId,
                VersionNumber = 1,
                Description = "Initial version",
                IsCurrent = false
            };

            var components1 = new ComponentResult
            {
                Success = true,
                Components = new List<SectionComponent>
                {
                    new SectionComponent
                    {
                        SectionId = "section1",
                        SectionName = "Introduction",
                        ComponentCode = "const Introduction = () => <div>Original intro</div>;"
                    }
                }
            };

            version1.SetComponentData(components1);
            version1.SetReportData(new Dictionary<string, object>
            {
                { "title", "Original Title" },
                { "version", "1.0" }
            });

            // Create version 2 (current)
            var version2 = new ReportVersion
            {
                Id = Guid.NewGuid(),
                ReportId = reportId,
                VersionNumber = 2,
                Description = "Updated version",
                IsCurrent = true
            };

            var components2 = new ComponentResult
            {
                Success = true,
                Components = new List<SectionComponent>
                {
                    new SectionComponent
                    {
                        SectionId = "section1",
                        SectionName = "Introduction",
                        ComponentCode = "const Introduction = () => <div>Updated intro</div>;"
                    }
                }
            };

            version2.SetComponentData(components2);
            version2.SetReportData(new Dictionary<string, object>
            {
                { "title", "Updated Title" },
                { "version", "2.0" },
                { "newField", "New data" }
            });

            _context.ReportVersions.AddRange(version1, version2);
            await _context.SaveChangesAsync();
        }

        private async Task CreateTestReportWithDifferentComponents(Guid reportId)
        {
            await CreateTestReport(reportId);

            // Version 1 with original components
            var version1 = new ReportVersion
            {
                Id = Guid.NewGuid(),
                ReportId = reportId,
                VersionNumber = 1,
                Description = "Version with original components",
                IsCurrent = false
            };

            var components1 = new ComponentResult
            {
                Success = true,
                Components = new List<SectionComponent>
                {
                    new SectionComponent
                    {
                        SectionId = "section1",
                        SectionName = "Section 1",
                        ComponentCode = "const Section1 = () => <div>Section 1 content</div>;"
                    }
                }
            };

            version1.SetComponentData(components1);

            // Version 2 with modified and new components
            var version2 = new ReportVersion
            {
                Id = Guid.NewGuid(),
                ReportId = reportId,
                VersionNumber = 2,
                Description = "Version with modified components",
                IsCurrent = true
            };

            var components2 = new ComponentResult
            {
                Success = true,
                Components = new List<SectionComponent>
                {
                    new SectionComponent
                    {
                        SectionId = "section1",
                        SectionName = "Section 1 Updated",
                        ComponentCode = "const Section1 = () => <div>Updated section 1 content</div>;"
                    },
                    new SectionComponent
                    {
                        SectionId = "section2",
                        SectionName = "Section 2",
                        ComponentCode = "const Section2 = () => <div>New section 2 content</div>;"
                    }
                }
            };

            version2.SetComponentData(components2);

            _context.ReportVersions.AddRange(version1, version2);
            await _context.SaveChangesAsync();
        }

        private async Task CreateTestReportWithDataChanges(Guid reportId)
        {
            await CreateTestReport(reportId);

            // Version 1 with original data
            var version1 = new ReportVersion
            {
                Id = Guid.NewGuid(),
                ReportId = reportId,
                VersionNumber = 1,
                Description = "Version with original data",
                IsCurrent = false
            };

            version1.SetReportData(new Dictionary<string, object>
            {
                { "field1", "value1" },
                { "field2", "value2" }
            });

            // Version 2 with additional data
            var version2 = new ReportVersion
            {
                Id = Guid.NewGuid(),
                ReportId = reportId,
                VersionNumber = 2,
                Description = "Version with additional data",
                IsCurrent = true
            };

            version2.SetReportData(new Dictionary<string, object>
            {
                { "field1", "updated_value1" },
                { "field2", "value2" },
                { "field3", "new_value3" },
                { "field4", "new_value4" }
            });

            _context.ReportVersions.AddRange(version1, version2);
            await _context.SaveChangesAsync();
        }

        private async Task CreateTestReport(Guid reportId)
        {
            var client = new Client
            {
                Id = Guid.NewGuid(),
                Name = "Test Client",
                Email = "<EMAIL>"
            };

            var template = new Template
            {
                Id = Guid.NewGuid(),
                Name = "Test Template",
                Description = "Test template for version comparison tests"
            };

            var report = new Report
            {
                Id = reportId,
                Name = "Test Report",
                Description = "Test report for version comparison tests",
                ClientId = client.Id,
                Client = client,
                TemplateId = template.Id,
                Template = template
            };

            _context.Clients.Add(client);
            _context.Templates.Add(template);
            _context.Reports.Add(report);
            await _context.SaveChangesAsync();
        }

        private void SeedTestData()
        {
            // Add any common test data here if needed
        }

        public void Dispose()
        {
            _context.Dispose();
        }
    }
}
