using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using FY.WB.CSHero2.Domain.Interfaces;

namespace FY.WB.CSHero2.ReportRenderingEngine.Application.Services
{
    /// <summary>
    /// Section configuration for component building
    /// </summary>
    public class SectionConfiguration
    {
        public string Id { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty;
        public Dictionary<string, object> Layout { get; set; } = new Dictionary<string, object>();
        public Dictionary<string, object> Styling { get; set; } = new Dictionary<string, object>();
        public List<string> RequiredFields { get; set; } = new List<string>();
        public List<string> OptionalFields { get; set; } = new List<string>();
        public Dictionary<string, object> ValidationRules { get; set; } = new Dictionary<string, object>();
        public Dictionary<string, object> DefaultValues { get; set; } = new Dictionary<string, object>();
    }

    /// <summary>
    /// Component styling information
    /// </summary>
    public class ComponentStyling
    {
        public string Framework { get; set; } = "TailwindCSS";
        public string Theme { get; set; } = "Default";
        public List<string> CssClasses { get; set; } = new List<string>();
        public Dictionary<string, string> InlineStyles { get; set; } = new Dictionary<string, string>();
        public Dictionary<string, object> Responsive { get; set; } = new Dictionary<string, object>();
        public Dictionary<string, string> Colors { get; set; } = new Dictionary<string, string>();
    }

    /// <summary>
    /// Service for building React components from templates and data
    /// </summary>
    public class ComponentBuilder
    {
        private readonly ILogger<ComponentBuilder> _logger;

        public ComponentBuilder(ILogger<ComponentBuilder> logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// Builds a complete component structure from template and data
        /// </summary>
        public async Task<ComponentResult> BuildComponentsAsync(
            ComponentRequest request,
            CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogInformation("Building components for ReportId: {ReportId}", request.ReportId);

                var result = new ComponentResult
                {
                    Success = true,
                    Metadata = new ComponentMetadata
                    {
                        Framework = request.Options.Framework,
                        StyleFramework = request.Options.StyleFramework,
                        GeneratedAt = DateTime.UtcNow
                    },
                    Data = request.Data
                };

                // Determine sections to build
                var sectionsToProcess = GetSectionsToProcess(request);

                foreach (var sectionConfig in sectionsToProcess)
                {
                    var component = await BuildSectionComponentAsync(sectionConfig, request, cancellationToken);
                    if (component != null)
                    {
                        result.Components.Add(component);
                    }
                }

                _logger.LogInformation("Successfully built {ComponentCount} components for ReportId: {ReportId}",
                    result.Components.Count, request.ReportId);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error building components for ReportId: {ReportId}", request.ReportId);
                return new ComponentResult
                {
                    Success = false,
                    Errors = new List<string> { $"Component building failed: {ex.Message}" }
                };
            }
        }

        /// <summary>
        /// Builds a single section component
        /// </summary>
        public async Task<SectionComponent?> BuildSectionComponentAsync(
            SectionConfiguration sectionConfig,
            ComponentRequest request,
            CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Building component for section: {SectionId}", sectionConfig.Id);

                var component = new SectionComponent
                {
                    SectionId = sectionConfig.Id,
                    SectionName = sectionConfig.Name
                };

                // Extract section-specific data
                var sectionData = ExtractSectionData(request.Data, sectionConfig);

                // Generate component code based on section type
                component.ComponentCode = await GenerateComponentCodeAsync(sectionConfig, sectionData, request.Options);

                // Generate TypeScript definitions
                component.TypeDefinitions = GenerateTypeDefinitions(sectionConfig, sectionData, request.Options);

                // Generate imports
                component.Imports = GenerateImports(sectionConfig, request.Options);

                // Set component props
                component.Props = GenerateComponentProps(sectionConfig, sectionData);

                // Set component metadata
                component.Metadata = GenerateComponentMetadata(sectionConfig, request.Options);

                _logger.LogDebug("Successfully built component for section: {SectionId}", sectionConfig.Id);

                return component;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error building component for section: {SectionId}", sectionConfig.Id);
                return null;
            }
        }

        /// <summary>
        /// Generates React component code for a section
        /// </summary>
        private async Task<string> GenerateComponentCodeAsync(
            SectionConfiguration sectionConfig,
            Dictionary<string, object> sectionData,
            ComponentGenerationOptions options)
        {
            var componentName = ToPascalCase(sectionConfig.Name);
            var propsInterface = $"{componentName}Props";

            var codeBuilder = new StringBuilder();

            // Add component header comment
            codeBuilder.AppendLine($"/**");
            codeBuilder.AppendLine($" * {sectionConfig.Name} Component");
            codeBuilder.AppendLine($" * Generated on {DateTime.UtcNow:yyyy-MM-dd HH:mm:ss}");
            codeBuilder.AppendLine($" */");
            codeBuilder.AppendLine();

            // Add component function
            if (options.UseTypeScript)
            {
                codeBuilder.AppendLine($"export const {componentName}: React.FC<{propsInterface}> = ({{ data, className, ...props }}) => {{");
            }
            else
            {
                codeBuilder.AppendLine($"export const {componentName} = ({{ data, className, ...props }}) => {{");
            }

            // Add component body based on section type
            var componentBody = GenerateComponentBody(sectionConfig, sectionData, options);
            codeBuilder.AppendLine(componentBody);

            codeBuilder.AppendLine("};");

            // Add default props if using TypeScript
            if (options.UseTypeScript)
            {
                codeBuilder.AppendLine();
                codeBuilder.AppendLine($"{componentName}.defaultProps = {{");
                codeBuilder.AppendLine("  className: '',");
                codeBuilder.AppendLine("};");
            }

            return codeBuilder.ToString();
        }

        /// <summary>
        /// Generates component body based on section type
        /// </summary>
        private string GenerateComponentBody(
            SectionConfiguration sectionConfig,
            Dictionary<string, object> sectionData,
            ComponentGenerationOptions options)
        {
            var indent = "  ";
            var body = new StringBuilder();

            switch (sectionConfig.Type.ToLower())
            {
                case "chart":
                    body.AppendLine($"{indent}return (");
                    body.AppendLine($"{indent}  <div className={{`chart-container ${{className}}`}} {{...props}}>");
                    body.AppendLine($"{indent}    <h3 className=\"chart-title\">{sectionConfig.Name}</h3>");
                    body.AppendLine($"{indent}    <div className=\"chart-content\">");
                    body.AppendLine($"{indent}      {{/* Chart implementation will go here */}}");
                    body.AppendLine($"{indent}      <p>Chart data: {{JSON.stringify(data)}}</p>");
                    body.AppendLine($"{indent}    </div>");
                    body.AppendLine($"{indent}  </div>");
                    body.AppendLine($"{indent});");
                    break;

                case "table":
                    body.AppendLine($"{indent}return (");
                    body.AppendLine($"{indent}  <div className={{`table-container ${{className}}`}} {{...props}}>");
                    body.AppendLine($"{indent}    <h3 className=\"table-title\">{sectionConfig.Name}</h3>");
                    body.AppendLine($"{indent}    <div className=\"table-content\">");
                    body.AppendLine($"{indent}      {{/* Table implementation will go here */}}");
                    body.AppendLine($"{indent}      <p>Table data: {{JSON.stringify(data)}}</p>");
                    body.AppendLine($"{indent}    </div>");
                    body.AppendLine($"{indent}  </div>");
                    body.AppendLine($"{indent});");
                    break;

                case "text":
                    body.AppendLine($"{indent}return (");
                    body.AppendLine($"{indent}  <div className={{`text-section ${{className}}`}} {{...props}}>");
                    body.AppendLine($"{indent}    <h3 className=\"section-title\">{sectionConfig.Name}</h3>");
                    body.AppendLine($"{indent}    <div className=\"text-content\">");
                    body.AppendLine($"{indent}      {{data.content && <p>{{data.content}}</p>}}");
                    body.AppendLine($"{indent}    </div>");
                    body.AppendLine($"{indent}  </div>");
                    body.AppendLine($"{indent});");
                    break;

                default:
                    body.AppendLine($"{indent}return (");
                    body.AppendLine($"{indent}  <div className={{`section-container ${{className}}`}} {{...props}}>");
                    body.AppendLine($"{indent}    <h3 className=\"section-title\">{sectionConfig.Name}</h3>");
                    body.AppendLine($"{indent}    <div className=\"section-content\">");
                    body.AppendLine($"{indent}      <pre>{{JSON.stringify(data, null, 2)}}</pre>");
                    body.AppendLine($"{indent}    </div>");
                    body.AppendLine($"{indent}  </div>");
                    body.AppendLine($"{indent});");
                    break;
            }

            return body.ToString();
        }

        /// <summary>
        /// Generates TypeScript type definitions
        /// </summary>
        private string GenerateTypeDefinitions(
            SectionConfiguration sectionConfig,
            Dictionary<string, object> sectionData,
            ComponentGenerationOptions options)
        {
            if (!options.UseTypeScript) return string.Empty;

            var componentName = ToPascalCase(sectionConfig.Name);
            var propsInterface = $"{componentName}Props";

            var typeBuilder = new StringBuilder();
            typeBuilder.AppendLine($"interface {propsInterface} {{");
            typeBuilder.AppendLine("  data: any;");
            typeBuilder.AppendLine("  className?: string;");
            typeBuilder.AppendLine("  [key: string]: any;");
            typeBuilder.AppendLine("}");

            return typeBuilder.ToString();
        }

        /// <summary>
        /// Generates import statements
        /// </summary>
        private List<string> GenerateImports(SectionConfiguration sectionConfig, ComponentGenerationOptions options)
        {
            var imports = new List<string>();

            // React import
            if (options.UseTypeScript)
            {
                imports.Add("import React from 'react';");
            }
            else
            {
                imports.Add("import React from 'react';");
            }

            // Add framework-specific imports
            switch (options.StyleFramework.ToLower())
            {
                case "tailwindcss":
                    // TailwindCSS doesn't require imports
                    break;
                case "styled-components":
                    imports.Add("import styled from 'styled-components';");
                    break;
                case "emotion":
                    imports.Add("import { css } from '@emotion/react';");
                    break;
            }

            // Add section-specific imports
            switch (sectionConfig.Type.ToLower())
            {
                case "chart":
                    imports.Add("import { Chart } from '@/components/Chart';");
                    break;
                case "table":
                    imports.Add("import { Table } from '@/components/Table';");
                    break;
            }

            return imports;
        }

        /// <summary>
        /// Generates component props
        /// </summary>
        private Dictionary<string, object> GenerateComponentProps(
            SectionConfiguration sectionConfig,
            Dictionary<string, object> sectionData)
        {
            var props = new Dictionary<string, object>
            {
                ["sectionId"] = sectionConfig.Id,
                ["sectionName"] = sectionConfig.Name,
                ["sectionType"] = sectionConfig.Type,
                ["data"] = sectionData
            };

            return props;
        }

        /// <summary>
        /// Generates component metadata
        /// </summary>
        private Dictionary<string, object> GenerateComponentMetadata(
            SectionConfiguration sectionConfig,
            ComponentGenerationOptions options)
        {
            return new Dictionary<string, object>
            {
                ["sectionType"] = sectionConfig.Type,
                ["framework"] = options.Framework,
                ["styleFramework"] = options.StyleFramework,
                ["useTypeScript"] = options.UseTypeScript,
                ["generatedAt"] = DateTime.UtcNow.ToString("O")
            };
        }

        /// <summary>
        /// Generates component styling information
        /// </summary>
        private ComponentStyling GenerateComponentStyling(
            SectionConfiguration sectionConfig,
            ComponentGenerationOptions options)
        {
            return new ComponentStyling
            {
                Framework = options.StyleFramework,
                Theme = options.Theme,
                CssClasses = GenerateDefaultCssClasses(sectionConfig),
                Colors = new Dictionary<string, string>
                {
                    ["primary"] = "#2563eb",
                    ["secondary"] = "#64748b",
                    ["accent"] = "#f59e0b"
                }
            };
        }

        /// <summary>
        /// Generates default CSS classes for a section
        /// </summary>
        private List<string> GenerateDefaultCssClasses(SectionConfiguration sectionConfig)
        {
            var classes = new List<string>
            {
                "section-container",
                $"section-{sectionConfig.Type.ToLower()}",
                $"section-{sectionConfig.Id.ToLower().Replace(" ", "-")}"
            };

            return classes;
        }

        /// <summary>
        /// Extracts section-specific data from the full data set
        /// </summary>
        private Dictionary<string, object> ExtractSectionData(
            Dictionary<string, object> fullData,
            SectionConfiguration sectionConfig)
        {
            var sectionData = new Dictionary<string, object>();

            // Extract data for required fields
            foreach (var field in sectionConfig.RequiredFields)
            {
                if (fullData.TryGetValue(field, out var value))
                {
                    sectionData[field] = value;
                }
                else if (sectionConfig.DefaultValues.TryGetValue(field, out var defaultValue))
                {
                    sectionData[field] = defaultValue;
                }
            }

            // Extract data for optional fields
            foreach (var field in sectionConfig.OptionalFields)
            {
                if (fullData.TryGetValue(field, out var value))
                {
                    sectionData[field] = value;
                }
            }

            return sectionData;
        }

        /// <summary>
        /// Gets sections to process based on request configuration
        /// </summary>
        private List<SectionConfiguration> GetSectionsToProcess(ComponentRequest request)
        {
            // For now, return default sections based on template
            // This would be enhanced to read from actual template configuration
            return new List<SectionConfiguration>
            {
                new SectionConfiguration
                {
                    Id = "header",
                    Name = "Header Section",
                    Type = "text",
                    RequiredFields = new List<string> { "title", "date" },
                    OptionalFields = new List<string> { "subtitle", "author" }
                },
                new SectionConfiguration
                {
                    Id = "summary",
                    Name = "Executive Summary",
                    Type = "text",
                    RequiredFields = new List<string> { "summary" },
                    OptionalFields = new List<string> { "highlights" }
                },
                new SectionConfiguration
                {
                    Id = "financials",
                    Name = "Financial Overview",
                    Type = "chart",
                    RequiredFields = new List<string> { "revenue", "expenses" },
                    OptionalFields = new List<string> { "profit", "growth" }
                }
            };
        }

        /// <summary>
        /// Validates all generated components
        /// </summary>
        private async Task ValidateComponentsAsync(ComponentResult result, CancellationToken cancellationToken)
        {
            foreach (var component in result.Components)
            {
                // Basic validation checks
                if (string.IsNullOrEmpty(component.ComponentCode))
                {
                    result.Errors.Add($"Component code is empty for section {component.SectionId}");
                    result.Success = false;
                }

                if (!component.ComponentCode.Contains("return"))
                {
                    result.Warnings.Add($"Component for section {component.SectionId} should have a return statement");
                }
            }

            await Task.CompletedTask; // Make method async-compatible
        }

        /// <summary>
        /// Converts string to PascalCase
        /// </summary>
        private string ToPascalCase(string input)
        {
            if (string.IsNullOrEmpty(input)) return input;

            var words = input.Split(new[] { ' ', '-', '_' }, StringSplitOptions.RemoveEmptyEntries);
            var result = new StringBuilder();

            foreach (var word in words)
            {
                if (word.Length > 0)
                {
                    result.Append(char.ToUpper(word[0]));
                    if (word.Length > 1)
                    {
                        result.Append(word.Substring(1).ToLower());
                    }
                }
            }

            return result.ToString();
        }
    }
}
