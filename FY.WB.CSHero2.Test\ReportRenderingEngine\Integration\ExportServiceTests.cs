using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Xunit;
using FY.WB.CSHero2.Domain.Entities;
using FY.WB.CSHero2.Domain.Interfaces;
using FY.WB.CSHero2.Infrastructure.Persistence;
using FY.WB.CSHero2.ReportRenderingEngine.Application.Services;
using FY.WB.CSHero2.ReportRenderingEngine.Infrastructure.Services;

namespace FY.WB.CSHero2.Test.ReportRenderingEngine.Integration
{
    /// <summary>
    /// Integration tests for Export Service
    /// </summary>
    public class ExportServiceTests : IDisposable
    {
        private readonly ApplicationDbContext _context;
        private readonly ExportServiceImpl _exportService;
        private readonly TestReportService _reportService;
        private readonly TestVersioningService _versioningService;
        private readonly ILogger<ExportServiceImpl> _logger;
        private readonly List<IExportProvider> _exportProviders;

        public ExportServiceTests()
        {
            var options = new DbContextOptionsBuilder<ApplicationDbContext>()
                .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
                .Options;

            _context = new ApplicationDbContext(options);
            _logger = new LoggerFactory().CreateLogger<ExportServiceImpl>();
            
            // Create test services
            _reportService = new TestReportService();
            _versioningService = new TestVersioningService();
            
            // Create export providers
            var pdfLogger = new LoggerFactory().CreateLogger<PdfExportProvider>();
            var htmlLogger = new LoggerFactory().CreateLogger<HtmlExportProvider>();
            
            _exportProviders = new List<IExportProvider>
            {
                new PdfExportProvider(pdfLogger),
                new HtmlExportProvider(htmlLogger)
            };

            _exportService = new ExportServiceImpl(
                _context,
                _reportService,
                _versioningService,
                _logger,
                _exportProviders);

            // Seed test data
            SeedTestData();
        }

        [Fact]
        public async Task ExportToPdfAsync_ShouldGeneratePdfContent()
        {
            // Arrange
            var reportId = Guid.NewGuid();
            await CreateTestReportWithVersion(reportId);

            var options = new ExportOptions
            {
                PaperSize = "A4",
                Orientation = "Portrait",
                Quality = "High",
                IncludeImages = true,
                IncludeCoverPage = true,
                IncludeTableOfContents = true
            };

            // Act
            var result = await _exportService.ExportToPdfAsync(reportId, options);

            // Assert
            Assert.NotNull(result);
            Assert.True(result.Length > 0);
            
            // Verify it contains PDF-like content (basic check)
            var content = System.Text.Encoding.UTF8.GetString(result);
            Assert.Contains("PDF", content);
        }

        [Fact]
        public async Task ExportToHtmlAsync_ShouldGenerateHtmlContent()
        {
            // Arrange
            var reportId = Guid.NewGuid();
            await CreateTestReportWithVersion(reportId);

            var options = new ExportOptions
            {
                IncludeImages = true,
                CustomStyling = new Dictionary<string, string>
                {
                    { "custom-header", "color: blue; font-size: 24px;" }
                }
            };

            // Act
            var result = await _exportService.ExportToHtmlAsync(reportId, options);

            // Assert
            Assert.NotNull(result);
            Assert.Contains("<!DOCTYPE html>", result);
            Assert.Contains("<html", result);
            Assert.Contains("</html>", result);
            Assert.Contains("Test Report", result);
        }

        [Fact]
        public async Task GetSupportedFormatsAsync_ShouldReturnAvailableFormats()
        {
            // Act
            var formats = await _exportService.GetSupportedFormatsAsync();

            // Assert
            Assert.NotNull(formats);
            Assert.Contains("PDF", formats);
            Assert.Contains("HTML", formats);
            Assert.Equal(2, formats.Count);
        }

        [Fact]
        public async Task GetExportCapabilitiesAsync_ShouldReturnFormatCapabilities()
        {
            // Act
            var pdfCapabilities = await _exportService.GetExportCapabilitiesAsync("PDF");
            var htmlCapabilities = await _exportService.GetExportCapabilitiesAsync("HTML");

            // Assert - PDF capabilities
            Assert.NotNull(pdfCapabilities);
            Assert.Equal("PDF", pdfCapabilities.Format);
            Assert.True(pdfCapabilities.SupportsImages);
            Assert.True(pdfCapabilities.SupportsCharts);
            Assert.False(pdfCapabilities.SupportsInteractivity);
            Assert.True(pdfCapabilities.SupportsPasswordProtection);

            // Assert - HTML capabilities
            Assert.NotNull(htmlCapabilities);
            Assert.Equal("HTML", htmlCapabilities.Format);
            Assert.True(htmlCapabilities.SupportsImages);
            Assert.True(htmlCapabilities.SupportsCharts);
            Assert.True(htmlCapabilities.SupportsInteractivity);
            Assert.False(htmlCapabilities.SupportsPasswordProtection);
        }

        [Fact]
        public async Task PreviewExportAsync_ShouldGeneratePreviewInformation()
        {
            // Arrange
            var reportId = Guid.NewGuid();
            await CreateTestReportWithVersion(reportId);

            var options = new ExportOptions
            {
                IncludeImages = true,
                Quality = "High"
            };

            // Act
            var preview = await _exportService.PreviewExportAsync(reportId, "PDF", options);

            // Assert
            Assert.NotNull(preview);
            Assert.Equal("PDF", preview.Format);
            Assert.True(preview.EstimatedPageCount > 0);
            Assert.True(preview.EstimatedFileSize > 0);
            Assert.NotNull(preview.IncludedSections);
            Assert.NotEmpty(preview.IncludedSections);
        }

        [Fact]
        public async Task ExportMultipleReportsAsync_ShouldCreateArchive()
        {
            // Arrange
            var reportId1 = Guid.NewGuid();
            var reportId2 = Guid.NewGuid();
            await CreateTestReportWithVersion(reportId1, "Report 1");
            await CreateTestReportWithVersion(reportId2, "Report 2");

            var reportIds = new List<Guid> { reportId1, reportId2 };
            var options = new ExportOptions { Quality = "Medium" };

            // Act
            var archive = await _exportService.ExportMultipleReportsAsync(reportIds, "PDF", options);

            // Assert
            Assert.NotNull(archive);
            Assert.True(archive.Length > 0);
            
            // Verify archive content (basic check)
            var content = System.Text.Encoding.UTF8.GetString(archive);
            Assert.Contains("Archive", content);
        }

        [Fact]
        public async Task ScheduleExportAsync_ShouldReturnJobId()
        {
            // Arrange
            var reportId = Guid.NewGuid();
            await CreateTestReportWithVersion(reportId);

            var options = new ExportOptions();
            var scheduledFor = DateTime.UtcNow.AddHours(1);

            // Act
            var jobId = await _exportService.ScheduleExportAsync(reportId, "PDF", options, scheduledFor);

            // Assert
            Assert.NotEqual(Guid.Empty, jobId);
        }

        [Fact]
        public async Task ExportToPdfAsync_WithCustomOptions_ShouldApplyOptions()
        {
            // Arrange
            var reportId = Guid.NewGuid();
            await CreateTestReportWithVersion(reportId);

            var options = new ExportOptions
            {
                PaperSize = "A3",
                Orientation = "Landscape",
                Quality = "Low",
                IncludeImages = false,
                Password = "test123",
                Watermark = "CONFIDENTIAL",
                ExcludeSections = new List<string> { "section2" }
            };

            // Act
            var result = await _exportService.ExportToPdfAsync(reportId, options);

            // Assert
            Assert.NotNull(result);
            Assert.True(result.Length > 0);
            
            var content = System.Text.Encoding.UTF8.GetString(result);
            Assert.Contains("test123", content); // Password should be mentioned
            Assert.Contains("CONFIDENTIAL", content); // Watermark should be mentioned
        }

        [Fact]
        public async Task ExportToHtmlAsync_WithResponsiveDesign_ShouldIncludeResponsiveElements()
        {
            // Arrange
            var reportId = Guid.NewGuid();
            await CreateTestReportWithVersion(reportId);

            var options = new ExportOptions
            {
                IncludeImages = true,
                Quality = "High"
            };

            // Act
            var result = await _exportService.ExportToHtmlAsync(reportId, options);

            // Assert
            Assert.NotNull(result);
            Assert.Contains("viewport", result); // Should include viewport meta tag
            Assert.Contains("@media", result); // Should include responsive CSS
            Assert.Contains("flex", result); // Should include modern CSS
        }

        [Fact]
        public async Task ExportToPdfAsync_WithNonExistentReport_ShouldThrowException()
        {
            // Arrange
            var nonExistentReportId = Guid.NewGuid();
            var options = new ExportOptions();

            // Act & Assert
            await Assert.ThrowsAsync<InvalidOperationException>(
                () => _exportService.ExportToPdfAsync(nonExistentReportId, options));
        }

        private async Task CreateTestReportWithVersion(Guid reportId, string reportName = "Test Report")
        {
            var client = new Client
            {
                Id = Guid.NewGuid(),
                Name = "Test Client",
                Email = "<EMAIL>"
            };

            var template = new Template
            {
                Id = Guid.NewGuid(),
                Name = "Test Template",
                Description = "Test template for export tests"
            };

            var report = new Report
            {
                Id = reportId,
                Name = reportName,
                Description = "Test report for export tests",
                ClientId = client.Id,
                Client = client,
                TemplateId = template.Id,
                Template = template
            };

            // Create a test version with component data
            var version = new ReportVersion
            {
                Id = Guid.NewGuid(),
                ReportId = reportId,
                VersionNumber = 1,
                Description = "Test version",
                IsCurrent = true
            };

            // Set test component data
            var componentResult = new ComponentResult
            {
                Success = true,
                Components = new List<SectionComponent>
                {
                    new SectionComponent
                    {
                        SectionId = "section1",
                        SectionName = "Introduction",
                        ComponentCode = "const Introduction = () => <div><h2>Introduction</h2><p>This is the introduction section.</p></div>;"
                    },
                    new SectionComponent
                    {
                        SectionId = "section2",
                        SectionName = "Data Analysis",
                        ComponentCode = "const DataAnalysis = () => <div><h2>Data Analysis</h2><p>This section contains data analysis.</p></div>;"
                    }
                }
            };

            version.SetComponentData(componentResult);
            version.SetReportData(new Dictionary<string, object>
            {
                { "title", reportName },
                { "date", DateTime.Now.ToString("yyyy-MM-dd") },
                { "summary", "Test report summary" }
            });

            report.CurrentVersionId = version.Id;

            _context.Clients.Add(client);
            _context.Templates.Add(template);
            _context.Reports.Add(report);
            _context.ReportVersions.Add(version);
            await _context.SaveChangesAsync();

            // Configure test services
            _versioningService.SetCurrentVersion(reportId, version);
        }

        private void SeedTestData()
        {
            // Add any common test data here if needed
        }

        public void Dispose()
        {
            _context.Dispose();
        }
    }

    // Test helper classes
    public class TestReportService : IReportService
    {
        public Task<Report> CreateReportAsync(CreateReportRequest request, CancellationToken cancellationToken = default)
        {
            throw new NotImplementedException();
        }

        public Task<Report> GetReportAsync(Guid reportId, CancellationToken cancellationToken = default)
        {
            throw new NotImplementedException();
        }

        public Task<List<Report>> GetReportsAsync(CancellationToken cancellationToken = default)
        {
            throw new NotImplementedException();
        }

        public Task<Report> UpdateReportAsync(Guid reportId, UpdateReportRequest request, CancellationToken cancellationToken = default)
        {
            throw new NotImplementedException();
        }

        public Task DeleteReportAsync(Guid reportId, CancellationToken cancellationToken = default)
        {
            throw new NotImplementedException();
        }
    }

    public class TestVersioningService : IVersioningService
    {
        private readonly Dictionary<Guid, ReportVersion> _currentVersions = new Dictionary<Guid, ReportVersion>();

        public void SetCurrentVersion(Guid reportId, ReportVersion version)
        {
            _currentVersions[reportId] = version;
        }

        public Task<ReportVersion> GetCurrentVersionAsync(Guid reportId, CancellationToken cancellationToken = default)
        {
            if (_currentVersions.TryGetValue(reportId, out var version))
            {
                return Task.FromResult(version);
            }
            throw new InvalidOperationException($"No current version found for report {reportId}");
        }

        public Task<ReportVersion> CreateVersionAsync(Guid reportId, ComponentResult components, string description, CancellationToken cancellationToken = default)
        {
            throw new NotImplementedException();
        }

        public Task<List<VersionHistoryEntry>> GetVersionHistoryAsync(Guid reportId, CancellationToken cancellationToken = default)
        {
            throw new NotImplementedException();
        }

        public Task<VersionComparison> CompareVersionsAsync(Guid reportId, int version1, int version2, CancellationToken cancellationToken = default)
        {
            throw new NotImplementedException();
        }

        public Task<ReportVersion> RollbackToVersionAsync(Guid reportId, int versionNumber, CancellationToken cancellationToken = default)
        {
            throw new NotImplementedException();
        }

        public Task DeleteVersionAsync(Guid reportId, int versionNumber, CancellationToken cancellationToken = default)
        {
            throw new NotImplementedException();
        }
    }
}
