<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>

    <IsPackable>false</IsPackable>
    <IsTestProject>true</IsTestProject>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="coverlet.collector" Version="6.0.0" />
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.8.0" />
    <PackageReference Include="xunit" Version="2.4.2" />
    <PackageReference Include="xunit.runner.visualstudio" Version="2.4.5" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.InMemory" Version="8.0.15" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="9.0.4" />
    <PackageReference Include="Microsoft.Extensions.Logging" Version="9.0.4" />
    <PackageReference Include="Microsoft.Extensions.Logging.Console" Version="9.0.4" />
    <PackageReference Include="System.Net.Http.Json" Version="8.0.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\FY.WB.CSHero2.Application\FY.WB.CSHero2.Application.csproj" />
    <ProjectReference Include="..\FY.WB.CSHero2.Domain\FY.WB.CSHero2.Domain.csproj" />
    <ProjectReference Include="..\FY.WB.CSHero2.Infrastructure\FY.WB.CSHero2.Infrastructure.csproj" />
    <ProjectReference Include="..\FY.WB.CSHero2.ReportRenderingEngine.Application\FY.WB.CSHero2.ReportRenderingEngine.Application.csproj" />
    <ProjectReference Include="..\FY.WB.CSHero2.ReportRenderingEngine.Domain\FY.WB.CSHero2.ReportRenderingEngine.Domain.csproj" />
    <ProjectReference Include="..\FY.WB.CSHero2.ReportRenderingEngine.Infrastructure\FY.WB.CSHero2.ReportRenderingEngine.Infrastructure.csproj" />
  </ItemGroup>



</Project>
